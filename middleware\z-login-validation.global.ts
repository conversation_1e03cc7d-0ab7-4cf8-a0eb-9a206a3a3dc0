// Global middleware to handle Google OAuth validation on login page
export default defineNuxtRouteMiddleware(async (to) => {
  // Only run on login page with validate query parameter
  if (to.path !== '/auth/login' || to.query.validate !== 'true') {
    return
  }

  // Skip on server-side to avoid blocking initial page load
  if (process.server) {
    return
  }

  // Add small delay to avoid blocking page render
  await new Promise(resolve => setTimeout(resolve, 50))

  const supabase = useSupabaseClient()
  
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      // No session or error - redirect to login with error
      return navigateTo('/auth/login?error=session-invalid')
    }

    // Check if user has schools (is admin) with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 second timeout

    try {
      const response = await $fetch('/api/schools/user-schools', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.success || response.schools.length === 0) {
        // NOT a school admin - sign out and redirect
        await supabase.auth.signOut({ scope: 'global' })
        return navigateTo('/auth/login?error=not-school-admin')
      }

      // IS a school admin - redirect to dashboard
      const primarySchool = response.schools[0]
      return navigateTo(`/${primarySchool.code}`)

    } catch (fetchError) {
      clearTimeout(timeoutId)
      // If fetch fails (network, timeout, etc), assume not admin
      await supabase.auth.signOut({ scope: 'global' })
      return navigateTo('/auth/login?error=validation-timeout')
    }

  } catch (error) {
    console.error('Login validation error:', error)
    await supabase.auth.signOut({ scope: 'global' })
    return navigateTo('/auth/login?error=validation-failed')
  }
})
