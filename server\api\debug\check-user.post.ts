// Debug endpoint to check user lookup by email
import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { email } = body

    if (!email) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email is required'
      })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Try to find user by email using listUsers
    const { data: existingUsers } = await supabase.auth.admin.listUsers()
    const existingUser = existingUsers.users.find(u => u.email === email)

    // Also check pre_billing table
    const { data: preBillingData, error: preBillingError } = await supabase
      .from('pre_billing')
      .select('*')
      .eq('admin_email', email)
      .order('created_at', { ascending: false })
      .limit(1)

    return {
      success: true,
      email,
      userFound: !!existingUser,
      user: existingUser ? {
        id: existingUser.id,
        email: existingUser.email,
        email_confirmed_at: existingUser.email_confirmed_at,
        created_at: existingUser.created_at,
        user_metadata: existingUser.user_metadata
      } : null,
      preBillingFound: !!preBillingData && preBillingData.length > 0,
      preBillingData: preBillingData || null,
      preBillingError,
      totalUsers: existingUsers.users.length
    }

  } catch (error: any) {
    console.error('Debug check-user error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Debug check failed'
    })
  }
})
