<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <Icon name="heroicons:academic-cap" class="mx-auto h-12 w-12 text-blue-600" />
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          Daftar sebagai Guru
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          <PERSON><PERSON> {{ schoolName }} sebagai guru
        </p>
      </div>

      <!-- Registration Form -->
      <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="fullName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              <PERSON><PERSON>
            </label>
            <input id="fullName" v-model="form.fullName" type="text" required placeholder="Masukkan nama penuh"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Alamat Email
            </label>
            <input id="email" v-model="form.email" type="email" required placeholder="Masukkan email anda"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Kata Laluan
            </label>
            <div class="mt-1 relative">
              <input id="password" v-model="form.password" :type="showPassword ? 'text' : 'password'" required
                placeholder="Masukkan kata laluan"
                class="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
              <button type="button" @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <Icon :name="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'"
                  class="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Sahkan Kata Laluan
            </label>
            <input id="confirmPassword" v-model="form.confirmPassword" type="password" required
              placeholder="Sahkan kata laluan"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="text-red-600 text-sm text-center">
          {{ errorMessage }}
        </div>

        <!-- Submit Button -->
        <div>
          <button type="submit" :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <Icon name="heroicons:arrow-path" class="h-5 w-5 animate-spin" />
            </span>
            {{ loading ? 'Menghantar...' : 'Hantar' }}
          </button>
        </div>
      </form>

      <!-- Login Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Sudah mempunyai akaun?
          <NuxtLink to="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
            Log masuk di sini
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Get school code from route params
const route = useRoute()
const schoolCode = route.params.school as string

// Get school name from route (no API call needed for auth pages)
const schoolName = schoolCode

// Form state
const form = ref({
  email: '',
  password: '',
  confirmPassword: '',
  fullName: ''
})

const showPassword = ref(false)
const loading = ref(false)
const errorMessage = ref('')

// Handle form submission
const handleSubmit = async () => {
  errorMessage.value = ''

  // Validation
  if (form.value.password !== form.value.confirmPassword) {
    errorMessage.value = 'Kata laluan tidak sepadan'
    return
  }

  if (form.value.password.length < 6) {
    errorMessage.value = 'Kata laluan mestilah sekurang-kurangnya 6 aksara'
    return
  }

  loading.value = true

  try {
    const supabase = useSupabaseClient()

    // Register user
    const { data, error } = await supabase.auth.signUp({
      email: form.value.email,
      password: form.value.password,
      options: {
        data: {
          full_name: form.value.fullName,
          school_code: schoolCode
        }
      }
    })

    if (error) {
      throw error
    }

    if (data.user) {
      // Create complete profile immediately
      await (supabase as any).from('profiles').upsert({
        id: data.user.id,
        full_name: form.value.fullName,
        email: form.value.email
      })

      // Redirect to dashboard
      await navigateTo('/dashboard')
    }

  } catch (error: any) {
    console.error('Registration error:', error)
    errorMessage.value = error.message || 'Ralat semasa mendaftar'
  } finally {
    loading.value = false
  }
}

// SEO
useHead({
  title: `Daftar Guru - ${schoolName}`,
  meta: [
    { name: 'description', content: `Daftar sebagai guru di ${schoolName}` }
  ]
})
</script>
