// School existence validation API endpoint
// Checks if a school with the given code exists in the database

import { createClient } from '@supabase/supabase-js'

// Server-side cache for school existence to reduce database queries
const serverSchoolCache = new Map<string, { exists: boolean, timestamp: number }>()
const SERVER_CACHE_DURATION = 60 * 60 * 1000 // 1 hour server-side cache

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event)
    const { code } = body

    // Validate input
    if (!code || typeof code !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'School code is required'
      })
    }

    const trimmedCode = code.trim().toLowerCase()

    // Check server-side cache first
    const cached = serverSchoolCache.get(trimmedCode)
    const now = Date.now()
    
    if (cached && (now - cached.timestamp) < SERVER_CACHE_DURATION) {
      return {
        exists: cached.exists,
        code: trimmedCode,
        message: cached.exists ? 'School found (cached)' : 'School not found (cached)',
        cached: true
      }
    }

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    )

    // Check if school exists
    const { data: school, error: dbError } = await supabase
      .from('schools')
      .select('id, code, name, subscription_status')
      .eq('code', trimmedCode)
      .single()

    if (dbError && dbError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw createError({
        statusCode: 500,
        statusMessage: 'Error checking school existence'
      })
    }

    if (!school) {
      // Cache negative result
      serverSchoolCache.set(trimmedCode, {
        exists: false,
        timestamp: now
      })
      
      return {
        exists: false,
        code: trimmedCode,
        message: 'School not found'
      }
    }

    // Cache positive result
    serverSchoolCache.set(trimmedCode, {
      exists: true,
      timestamp: now
    })

    // School exists
    return {
      exists: true,
      code: school.code,
      name: school.name,
      subscription_status: school.subscription_status,
      message: 'School found'
    }

  } catch (error: any) {
    console.error('School existence check error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during school existence check'
    })
  }
})
