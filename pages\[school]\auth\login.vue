<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <Icon name="heroicons:academic-cap" class="mx-auto h-12 w-12 text-blue-600" />
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          Sign in to {{ schoolName }}
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Access your teaching dashboard
        </p>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Email Address
            </label>
            <input id="email" v-model="form.email" type="email" required placeholder="Enter your email"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Password
            </label>
            <div class="mt-1 relative">
              <input id="password" v-model="form.password" :type="showPassword ? 'text' : 'password'" required
                placeholder="Enter your password"
                class="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
              <button type="button" @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <Icon :name="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'"
                  class="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input id="remember-me" v-model="form.rememberMe" type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              <label for="remember-me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Remember me
              </label>
            </div>

            <div class="text-sm">
              <NuxtLink :to="`/${schoolCode}/auth/forgot-password`"
                class="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="loginError"
          class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <Icon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-400 mr-2" />
            <p class="text-sm text-red-800 dark:text-red-200">{{ loginError }}</p>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <button type="submit" :disabled="isLoading || !isFormValid"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
            <Icon v-if="isLoading" name="heroicons:arrow-path" class="animate-spin h-4 w-4 mr-2" />
            {{ isLoading ? 'Signing in...' : 'Sign in' }}
          </button>
        </div>

        <!-- Divider -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">Or</span>
          </div>
        </div>

        <!-- Social Login (Optional) -->
        <div class="space-y-3">
          <button type="button" @click="signInWithGoogle" :disabled="isLoading"
            class="w-full flex justify-center items-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors">
            <Icon name="logos:google-icon" class="h-5 w-5 mr-2" />
            Continue with Google
          </button>
        </div>
      </form>

      <!-- Register Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Don't have an account?
          <NuxtLink to="/auth/daftar" class="font-medium text-blue-600 hover:text-blue-500">
            Daftar sebagai guru
          </NuxtLink>
        </p>
      </div>

      <!-- Admin Link -->
      <div class="text-center pt-4 border-t border-gray-200 dark:border-gray-700">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Are you a school administrator?
          <NuxtLink to="/admin/login" class="font-medium text-blue-600 hover:text-blue-500">
            Admin login
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Use landing layout for auth pages
definePageMeta({
  layout: 'landing' as any
  // Removed guest middleware - not needed and causing conflicts
})

// Get school code from route
const route = useRoute()
const schoolCode = computed(() => route.params.school as string)

// Form state
const form = ref({
  email: '',
  password: '',
  rememberMe: false
})

const showPassword = ref(false)
const isLoading = ref(false)
const loginError = ref('')
const schoolName = ref('')

// Composables
const supabase = useSupabaseClient()
const router = useRouter()

// Computed
const isFormValid = computed(() => {
  return form.value.email.trim() && form.value.password.trim()
})

// Methods
const fetchSchoolInfo = async () => {
  try {
    // TODO: Fetch school information by code
    // For now, use the school code as name
    schoolName.value = schoolCode.value.toUpperCase()
  } catch (error) {
    console.error('Error fetching school info:', error)
    schoolName.value = 'School'
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  loginError.value = ''

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: form.value.email,
      password: form.value.password
    })

    if (error) throw error

    // TODO: Validate user has access to this school
    // Redirect to dashboard
    await router.push('/dashboard')

  } catch (error: any) {
    loginError.value = error.message || 'Failed to sign in'
  } finally {
    isLoading.value = false
  }
}

const signInWithGoogle = async () => {
  if (isLoading.value) return

  isLoading.value = true
  loginError.value = ''

  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`
      }
    })

    if (error) throw error

  } catch (error: any) {
    loginError.value = error.message || 'Failed to sign in with Google'
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchSchoolInfo()
})

// SEO
useHead({
  title: `Sign in to ${schoolName.value} - RPHMate`,
  meta: [
    {
      name: 'description',
      content: `Sign in to access your teaching dashboard at ${schoolName.value}.`
    }
  ]
})
</script>

<style scoped>
/* Custom styles for school login page */
</style>
