-- Migration: Remove is_profile_complete column from profiles table
-- Created: 2025-07-18
-- Description: Remove is_profile_complete column as part of simplified registration flow

BEGIN;

-- =====================================================
-- REMOVE is_profile_complete COLUMN
-- =====================================================

-- Remove the is_profile_complete column from profiles table
ALTER TABLE profiles DROP COLUMN IF EXISTS is_profile_complete;

-- =====================================================
-- UPDATE COMMENTS
-- =====================================================

COMMENT ON TABLE profiles IS 'User profiles - simplified without profile completion tracking';

COMMIT;
