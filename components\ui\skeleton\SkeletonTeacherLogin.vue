<template>
    <div class="w-full max-w-sm space-y-6 sm:space-y-8">
        <!-- Welcome Header -->
        <div class="text-center">
            <SkeletonBox height="2rem" width="65%" class="mb-2 mx-auto" variant="medium" />
            <SkeletonBox height="0.875rem" width="85%" class="mx-auto" variant="light" />
        </div>

        <!-- Login Form -->
        <div class="space-y-6">
            <!-- Form Fields -->
            <div class="space-y-4">
                <!-- Email Field -->
                <div>
                    <SkeletonBox height="2.75rem" width="100%" class="rounded-md" variant="light" />
                </div>

                <!-- Password Field -->
                <div>
                    <SkeletonBox height="2.75rem" width="100%" class="rounded-md" variant="light" />
                </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <SkeletonBox height="1rem" width="1rem" class="rounded" variant="medium" />
                    <SkeletonBox height="0.875rem" width="5rem" variant="light" />
                </div>
                <SkeletonBox height="0.875rem" width="6rem" variant="light" />
            </div>

            <!-- Sign In Button -->
            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="medium" />

            <!-- Error Message Placeholder (hidden by default) -->
            <div class="opacity-0">
                <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
        </div>

        <!-- Divider Section -->
        <div class="space-y-4">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-200 dark:border-gray-600"></div>
                </div>
                <div class="relative flex justify-center">
                    <SkeletonBox height="0.875rem" width="7rem" class="bg-white dark:bg-gray-900 px-2"
                        variant="light" />
                </div>
            </div>

            <!-- Google Sign In Button -->
            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="light" />
        </div>

        <!-- Sign Up Link -->
        <div class="text-center">
            <SkeletonBox height="0.875rem" width="55%" class="mx-auto" variant="light" />
        </div>
    </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
</script>
