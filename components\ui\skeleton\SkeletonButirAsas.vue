<template>
    <div class="space-y-8 p-4 sm:p-6 lg:p-8">
        <!-- Header Section -->
        <div>
            <SkeletonBox height="2.25rem" width="70%" class="mx-auto mb-3" variant="medium" />
            <SkeletonBox height="1rem" width="85%" class="mx-auto" variant="light" />
        </div>

        <!-- Form Container -->
        <div class="mt-8 space-y-6 sm:space-y-8">

            <!-- Basic Info Fieldset -->
            <fieldset class="border border-gray-300 dark:border-gray-700 p-4 sm:p-6 rounded-md">
                <legend class="text-lg font-medium text-gray-900 dark:text-white px-2">
                    <SkeletonBox height="1.125rem" width="8rem" variant="medium" />
                </legend>

                <!-- Two-Column Layout for Profile Picture and Main Details -->
                <div class="flex flex-col md:flex-row gap-6 md:items-center">
                    <!-- Left Column: Profile Upload -->
                    <div class="md:w-1/3 flex flex-col items-center">
                        <div class="w-24 h-24 rounded-full overflow-hidden mb-2">
                            <SkeletonBox height="6rem" width="6rem" class="rounded-full" variant="light" />
                        </div>
                        <SkeletonBox height="0.875rem" width="5rem" class="mb-2" variant="light" />
                        <SkeletonBox height="0.75rem" width="8rem" variant="light" />
                    </div>

                    <!-- Right Column: Form Inputs -->
                    <div class="md:w-2/3 space-y-6">
                        <!-- Full Name Input -->
                        <div>
                            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="light" />
                        </div>

                        <!-- Gender Select -->
                        <div>
                            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="light" />
                        </div>

                        <!-- Role Select -->
                        <div>
                            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="light" />
                        </div>
                    </div>
                </div>
            </fieldset>

            <!-- Class Subject Fieldset -->
            <fieldset class="border border-gray-300 dark:border-gray-700 p-4 sm:p-6 rounded-md min-w-0">
                <legend class="text-lg font-medium text-gray-900 dark:text-white px-2">
                    <SkeletonBox height="1.125rem" width="7rem" variant="medium" />
                </legend>

                <!-- Class Subject Component Skeleton -->
                <div class="mt-4 space-y-4">
                    <!-- Add New Row -->
                    <div class="flex items-center gap-3">
                        <SkeletonBox height="2.5rem" width="30%" class="rounded-md" variant="light" />
                        <SkeletonBox height="2.5rem" width="30%" class="rounded-md" variant="light" />
                        <SkeletonBox height="2.5rem" width="25%" class="rounded-md" variant="light" />
                        <SkeletonBox height="2.5rem" width="4rem" class="rounded-md" variant="medium" />
                    </div>

                    <!-- Existing Entries (show 2-3 sample rows) -->
                    <div class="space-y-3">
                        <div v-for="n in 2" :key="n"
                            class="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                            <SkeletonBox height="1rem" width="25%" variant="light" />
                            <SkeletonBox height="1rem" width="25%" variant="light" />
                            <SkeletonBox height="1rem" width="20%" variant="light" />
                            <div class="flex gap-2">
                                <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" variant="medium" />
                                <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" variant="medium" />
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row-reverse gap-4 mt-6">
                <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="medium" />
                <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="light" />
            </div>

            <!-- Error/Success Message Placeholder (hidden by default) -->
            <div class="opacity-0">
                <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
</script>
