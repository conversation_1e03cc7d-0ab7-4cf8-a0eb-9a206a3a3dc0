<template>
    <div class="w-full max-w-sm space-y-6 sm:space-y-8">
        <!-- Create Account Header -->
        <div class="text-center">
            <SkeletonBox height="2rem" width="70%" class="mb-2 mx-auto" variant="medium" />
            <SkeletonBox height="0.875rem" width="80%" class="mx-auto" variant="light" />
        </div>

        <!-- Signup Form -->
        <div class="space-y-6">
            <!-- Form Fields -->
            <div class="space-y-4">
                <!-- Full Name Field -->
                <div>
                    <SkeletonBox height="2.75rem" width="100%" class="rounded-md" variant="light" />
                </div>

                <!-- Email Field -->
                <div>
                    <SkeletonBox height="2.75rem" width="100%" class="rounded-md" variant="light" />
                </div>

                <!-- Password Field -->
                <div>
                    <SkeletonBox height="2.75rem" width="100%" class="rounded-md" variant="light" />
                </div>

                <!-- Confirm Password Field -->
                <div>
                    <SkeletonBox height="2.75rem" width="100%" class="rounded-md" variant="light" />
                </div>
            </div>

            <!-- Terms & Conditions Checkbox -->
            <div class="flex items-start">
                <SkeletonBox height="1rem" width="1rem" class="rounded flex-shrink-0 mt-0.5" variant="medium" />
                <div class="ml-2 space-y-1">
                    <SkeletonBox height="0.875rem" width="12rem" variant="light" />
                    <SkeletonBox height="0.875rem" width="8rem" variant="light" />
                </div>
            </div>

            <!-- Submit Button -->
            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="medium" />

            <!-- Error/Success Message Placeholder (hidden by default) -->
            <div class="opacity-0">
                <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
        </div>

        <!-- Divider Section -->
        <div class="space-y-4">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-200 dark:border-gray-600"></div>
                </div>
                <div class="relative flex justify-center">
                    <SkeletonBox height="0.875rem" width="7rem" class="bg-white dark:bg-gray-900 px-2"
                        variant="light" />
                </div>
            </div>

            <!-- Google Sign Up Button -->
            <SkeletonBox height="3rem" width="100%" class="rounded-md" variant="light" />
        </div>

        <!-- Sign In Link -->
        <div class="text-center">
            <SkeletonBox height="0.875rem" width="60%" class="mx-auto" variant="light" />
        </div>
    </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
</script>
