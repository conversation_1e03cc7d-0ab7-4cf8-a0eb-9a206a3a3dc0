// Global middleware for subdomain detection and routing
// Created: 2025-07-13

import { navigateTo, defineNuxtRouteMiddleware } from "#app"
import { getHeader } from "h3"
import type { SubdomainInfo, RouteContext } from "~/types/multiTenant"

// Smart caching for school validation - prevents hydration issues
const schoolValidationCache = new Map<string, { exists: boolean, timestamp: number }>()
const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes - longer cache to reduce API calls

export default defineNuxtRouteMiddleware(async (to, from) => {
  // SECURE SUBDOMAIN ROUTING - Validate school existence FIRST

  // Get subdomain information
  const subdomainInfo = getSubdomainInfo()

  // CRITICAL: Block school app services on main domain
  if (subdomainInfo.isMainDomain) {
    const schoolAppServices = [
      '/rph', '/jadual-mengajar', '/jadual-pencerapan', '/kalendar-akademik',
      '/maklumat-guru', '/refleksi', '/rpt', '/takwim-tahunan', '/tugas-guru',
      '/dskp', '/kelas-subjek', '/dashboard'
    ]

    // If trying to access school services on main domain, redirect to landing page
    if (schoolAppServices.some(service => to.path === service || to.path.startsWith(service))) {
      return navigateTo('/')
    }

    // Allow main domain public pages
    const publicPaths = ['/', '/login', '/pricing', '/features', '/contact', '/billing', '/pembayaran', '/daftar', '/success']
    if (publicPaths.some(path => to.path === path || to.path.startsWith(path))) {
      return
    }
  }

  // CRITICAL SECURITY: Validate school exists BEFORE allowing ANY access
  if (subdomainInfo.isSchoolSubdomain) {
    // First, validate school exists for all routes
    const schoolExists = await validateSchoolExistsWithCache(subdomainInfo.schoolCode!)
    
    if (!schoolExists) {
      // School doesn't exist - show 404 for ANY route
      throw createError({
        statusCode: 404,
        statusMessage: 'School Not Found',
        data: {
          message: `School "${subdomainInfo.schoolCode}" does not exist.`,
          suggestion: 'Please check the school code or contact support.'
        }
      })
    }

    // Handle root path redirection for school subdomains
    if (to.path === '/') {
      const supabase = useSupabaseClient()
      
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (session) {
          // User is logged in, redirect to dashboard
          return navigateTo('/dashboard')
        } else {
          // User is not logged in, redirect to login
          return navigateTo('/auth/login')
        }
      } catch (error) {
        // On error, redirect to login
        console.error('Error checking session for school root:', error)
        return navigateTo('/auth/login')
      }
    }
    
    // OPTIMIZATION: For auth pages, school already validated above
    if (to.path.includes('/auth/')) {
      // School exists, allow auth page access immediately without further checks
      // Skip session validation to improve performance on login page
      // Use aggressive caching for auth pages to reduce validation overhead
      return
    }
    
    return await handleSecureSchoolRouting(to, subdomainInfo)
  }

  // Handle main domain routing
  if (subdomainInfo.isMainDomain) {
    const routeContext = {
      isLandingPage: false,
      isAdminDashboard: true,
      isSchoolApp: false,
      schoolCode: to.path.substring(1),
      requiredAuth: true
    }
    return await handleMainDomainRouting(to, routeContext)
  }
})

/**
 * Extract subdomain information from the current host
 */
function getSubdomainInfo(): SubdomainInfo {
  let host: string

  // Get host from server-side or client-side
  if (typeof window === 'undefined') {
    // Server-side: get host from Nuxt context
    const nuxtApp = useNuxtApp()
    const event = nuxtApp.ssrContext?.event
    host = event ? getHeader(event, 'host') || 'localhost:3000' : 'localhost:3000'
  } else {
    // Client-side: get host from window
    host = window.location.host
  }

  const parts = host.split('.')
  
  // For development (localhost:3000 or schoolcode.localhost:3000)
  if (host.includes('localhost')) {
    if (parts.length > 1 && parts[0] !== 'localhost') {
      const schoolCode = parts[0].toLowerCase()
      return {
        isSchoolSubdomain: true,
        schoolCode,
        isMainDomain: false,
        subdomain: schoolCode
      }
    }
    return {
      isSchoolSubdomain: false,
      schoolCode: null,
      isMainDomain: true,
      subdomain: null
    }
  }

  // For production (schoolcode.yourdomain.com)
  if (parts.length >= 3) {
    const schoolCode = parts[0].toLowerCase()
    // Exclude common subdomains that are not school codes
    const excludedSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp']
    
    if (!excludedSubdomains.includes(schoolCode)) {
      return {
        isSchoolSubdomain: true,
        schoolCode,
        isMainDomain: false,
        subdomain: schoolCode
      }
    }
  }

  return {
    isSchoolSubdomain: false,
    schoolCode: null,
    isMainDomain: true,
    subdomain: null
  }
}

/**
 * Determine the route context based on path and subdomain
 */
function determineRouteContext(path: string, subdomainInfo: SubdomainInfo): RouteContext {
  // Landing page routes (main domain)
  if (subdomainInfo.isMainDomain) {
    if (path === '/' || path.startsWith('/pricing') || path.startsWith('/billing') || path.startsWith('/features') || path.startsWith('/contact')) {
      return {
        isLandingPage: true,
        isAdminDashboard: false,
        isSchoolApp: false,
        schoolCode: null,
        requiredAuth: false
      }
    }

    // School admin login (main domain)
    if (path === '/login') {
      return {
        isLandingPage: false,
        isAdminDashboard: false,
        isSchoolApp: false,
        schoolCode: null,
        requiredAuth: false
      }
    }

    // School admin dashboard routes (main domain - /schoolcode)
    if (path.match(/^\/[a-zA-Z0-9]+$/)) {
      return {
        isLandingPage: false,
        isAdminDashboard: true,
        isSchoolApp: false,
        schoolCode: path.substring(1), // Extract school code from path
        requiredAuth: true,
        requiredRole: 'school_admin'
      }
    }
  }

  // School app routes (school subdomain)
  if (subdomainInfo.isSchoolSubdomain) {
    // School admin page (schoolcode.domain.com/admin)
    if (path === '/admin') {
      return {
        isLandingPage: false,
        isAdminDashboard: false,
        isSchoolApp: true,
        isSchoolAdmin: true,
        schoolCode: subdomainInfo.schoolCode,
        requiredAuth: true,
        requiredRole: 'school_admin'
      }
    }

    // Regular school app routes
    return {
      isLandingPage: false,
      isAdminDashboard: false,
      isSchoolApp: true,
      isSchoolAdmin: false,
      schoolCode: subdomainInfo.schoolCode,
      requiredAuth: !path.includes('/auth/'),
      requiredRole: 'teacher' // Default role, can be admin, supervisor, or teacher
    }
  }

  // Default context
  return {
    isLandingPage: true,
    isAdminDashboard: false,
    isSchoolApp: false,
    schoolCode: null,
    requiredAuth: false
  }
}

// OLD ROUTING LOGIC REMOVED - Now handled directly in main middleware

/**
 * SECURE School Routing - School validation already done, handle routing
 */
async function handleSecureSchoolRouting(to: any, subdomainInfo: SubdomainInfo) {
  // School existence already validated in main middleware
  // Just handle routing for protected routes
  
  if (!to.path.includes('/auth/')) {
    // Protected route - let pages handle authentication with proper middleware
    // Middleware should only validate school existence, not authentication
    return
  }

  // Auth routes - allow access (school already validated)
  return
}

/**
 * Smart school validation with caching - prevents hydration issues
 */
async function validateSchoolExistsWithCache(schoolCode: string): Promise<boolean> {
  // Check cache first
  const cached = schoolValidationCache.get(schoolCode)
  const now = Date.now()

  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    // Use cached result - no API call needed
    return cached.exists
  }

  try {
    // Make API call only when cache is expired or missing
    const response = await $fetch('/api/schools/exists', {
      method: 'POST',
      body: { code: schoolCode }
    }) as any

    const exists = response?.exists || false

    // Cache the result
    schoolValidationCache.set(schoolCode, {
      exists,
      timestamp: now
    })

    return exists

  } catch (error) {
    console.error(`Error validating school ${schoolCode}:`, error)

    // On error, assume school doesn't exist for security
    schoolValidationCache.set(schoolCode, {
      exists: false,
      timestamp: now
    })

    return false
  }
}

/**
 * Handle routing for main domain
 */
async function handleMainDomainRouting(to: any, routeContext: RouteContext) {
  const supabase = useSupabaseClient()

  // Handle school admin dashboard authentication (/schoolcode routes)
  if (routeContext.isAdminDashboard && routeContext.requiredAuth) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        return navigateTo('/login')
      }

      // Check if user has access to this school
      if (routeContext.schoolCode) {
        try {
          const response = await $fetch('/api/schools/validate-admin-access', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`
            },
            body: { schoolCode: routeContext.schoolCode }
          }) as any

          if (!response.hasAccess) {
            // User doesn't have admin access to this school
            return navigateTo('/login')
          }
        } catch (accessError) {
          console.error('Error validating school admin access:', accessError)
          return navigateTo('/login')
        }
      }

    } catch (error) {
      console.error('Error checking admin authentication:', error)
      return navigateTo('/login')
    }
  }

  // Redirect authenticated users away from login page
  if (to.path === '/login' && routeContext.requiredAuth === false) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        // Get user's schools and redirect to first one
        try {
          const response = await $fetch('/api/schools/user-schools', {
            headers: {
              'Authorization': `Bearer ${session.access_token}`
            }
          }) as any

          if (response.success && response.schools && response.schools.length > 0) {
            // Redirect to the first school's admin dashboard
            const firstSchool = response.schools[0]
            return navigateTo(`/${firstSchool.code}`)
          } else {
            // No schools found, redirect to pricing page to create one
            return navigateTo('/pricing')
          }
        } catch (error) {
          console.error('Error fetching user schools:', error)
          // Continue to login page if there's an error
        }
      }
    } catch (error) {
      // Continue to login page if there's an error
      console.error('Error checking session:', error)
    }
  }
}

/**
 * Utility function to check if a school code is valid
 * This will be used later when we implement school validation
 */
async function validateSchoolCode(schoolCode: string): Promise<boolean> {
  // TODO: Implement school code validation against database
  // For now, return true to allow development
  return true
}

/**
 * Utility function to check user's access to a school
 * This will be used later when we implement school membership validation
 */
async function checkSchoolAccess(userId: string, schoolCode: string): Promise<boolean> {
  // TODO: Implement school access validation
  // Check if user has active membership in the school
  return true
}
