// Debug endpoint to test school creation process
import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { 
      schoolCode,
      schoolName,
      schoolAddress,
      adminEmail,
      adminFirstName,
      adminLastName,
      selectedPlan
    } = body

    if (!schoolCode || !schoolName || !adminEmail || !selectedPlan) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields'
      })
    }

    console.log('🧪 Testing school creation with data:', {
      schoolCode,
      schoolName,
      adminEmail,
      selectedPlan
    })

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Step 1: Find existing user
    const { data: existingUsers } = await supabase.auth.admin.listUsers()
    const existingUser = existingUsers.users.find(u => u.email === adminEmail)

    let userId: string | undefined

    if (existingUser) {
      userId = existingUser.id
      console.log('👤 Found existing user:', userId)
    } else {
      console.log('👤 No existing user found, would create new user')
      // For testing, we won't actually create a new user
      return {
        success: false,
        message: 'User not found - registration may not have completed properly',
        adminEmail,
        existingUser: null,
        totalUsers: existingUsers.users.length
      }
    }

    // Step 2: Check if school already exists
    const { data: existingSchool } = await supabase
      .from('schools')
      .select('*')
      .eq('code', schoolCode.toLowerCase())
      .single()

    if (existingSchool) {
      return {
        success: false,
        message: 'School already exists',
        school: existingSchool
      }
    }

    // Step 3: Test school creation (dry run)
    console.log('🏫 Would create school with data:', {
      name: schoolName,
      code: schoolCode.toLowerCase(),
      admin_user_id: userId,
      location: schoolAddress || null,
      subscription_status: 'trialing',
      subscription_plan: selectedPlan
    })

    return {
      success: true,
      message: 'School creation test successful',
      userId,
      schoolData: {
        name: schoolName,
        code: schoolCode.toLowerCase(),
        admin_user_id: userId,
        location: schoolAddress || null,
        subscription_status: 'trialing',
        subscription_plan: selectedPlan
      },
      existingUser: {
        id: existingUser.id,
        email: existingUser.email,
        email_confirmed_at: existingUser.email_confirmed_at,
        user_metadata: existingUser.user_metadata
      }
    }

  } catch (error: any) {
    console.error('Debug test-school-creation error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Test failed'
    })
  }
})
